<form
    wire:submit="store"
    enctype="multipart/form-data"
>
    @if ($category == 'ankle_jewlery')
        @include('dashboard.admin.orders.create.category.ankle_jewlery')
    @endif
    @if ($category == 'bracelet')
        @include('dashboard.admin.orders.create.category.bracelet')
    @endif
    @if ($category == 'jewelery')
        @include('dashboard.admin.orders.create.category.jewelery')
    @endif
    @if ($category == 'jewelry')
        @include('dashboard.admin.orders.create.category.jewelry')
    @endif
    @if ($category == 'ring')
        @include('dashboard.admin.orders.create.category.ring')
    @endif
    @if ($category == 'spoon')
        @include('dashboard.admin.orders.create.category.spoon')
    @endif

    <div class="rounded-xl bg-white p-5 dark:bg-gray-900 max-md:px-2 md:mt-3 md:shadow">
        <div class="mb-3 border-b border-gray-200 pb-3 dark:border-gray-800">
            <h3 class="text-base font-bold text-gray-700 dark:text-gray-200">نحوه ارسال مرسوله</h3>
        </div>
        <div class="grid grid-cols-1 gap-3 md:grid-cols-10">
            <div class="md:col-span-4">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                    for="post_type"
                >نوع ارسال مرسوله:</label>
                <select
                    class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="post_type"
                    wire:model="data.post_type"
                >
                    <option selected>--انتخاب کنید--</option>
                    <option>پست</option>
                    {{-- <option value="تیباکس">تیباکس</option> --}}
                    {{-- <option value="پست ویژه">پست ویژه</option> --}}
                    {{-- <option>پیک</option> --}}
                    {{-- <option>حضوری</option> --}}
                    {{-- <option>حضوری الماس</option> --}}
                    {{-- <option>حضوری همیلا</option> --}}
                    <option>پیک ( تهران و حومه - به عهده مشتری )</option>
                    <option>تحویل حضوری شعبه پونک ( پاساژ همیلا )</option>
                    <option>تحویل حضوری شعبه جنت آباد ( پاساژ الماس )</option>
                </select>
                @error('data.post_type')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                    for="fullname_agent"
                >نام مامور پیک:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-sm outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="fullname_agent"
                    type="text"
                    wire:model="data.fullname_agent"
                >
                @error('data.fullname_agent')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                    for="phone"
                >شماره تماس مامور پیک:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="phone_agent"
                    type="tel"
                    wire:model="data.phone_agent"
                    dir="ltr"
                >
                @error('data.phone_agent')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-8">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                    for="tracking_code"
                >کدرهگیری ارسال مرسوله:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-yellow-200 p-2.5 text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="tracking_code"
                    type="tel"
                    dir="ltr"
                    wire:model="tracking_code"
                >
                @error('tracking_code')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
        </div>

    </div>
    <div class="mt-3 rounded-xl bg-red-500 p-5 shadow dark:bg-gray-900">
        <div class="mb-3 border-b border-red-400 pb-3">
            <h3 class="text-base text-white">تاریخ و وضعیت سفارش</h3>
        </div>
        <div class="grid grid-cols-1 gap-3 max-md:grid-cols-2 md:grid-cols-10">
            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm text-white"
                    for="order_register_date"
                >تاریخ ثبت سفارش:</label>
                <input
                    class="block w-full rounded-lg border-2 border-red-300 bg-red-500 p-2.5 text-center text-sm text-white outline-none focus:border-red-300 focus:ring-red-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="order_register_date"
                    data-jdp
                    type="text"
                    wire:model="data.order_register_date"
                    dir="ltr"
                >
                @error('data.order_register_date')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm text-white"
                    for="customer_date"
                >تاریخ مدنظر مشتری:</label>
                <input
                    class="block w-full rounded-lg border-2 border-red-300 bg-red-500 p-2.5 text-center text-sm text-white outline-none focus:border-red-300 focus:ring-red-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="customer_date"
                    data-jdp
                    type="text"
                    wire:model="data.customer_date"
                    dir="ltr"
                >
                @error('customer_date')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-3">
                <label
                    class="mb-2 block text-sm text-white"
                    for="last_status"
                >وضعیت سفارش:</label>
                <select
                    class="block w-full rounded-lg border-2 border-red-300 bg-red-500 p-2.5 text-center text-sm text-white outline-none focus:border-red-300 focus:ring-red-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="last_status"
                    wire:model="data.last_status"
                >
                    <option selected>--انتخاب کنید--</option>
                    <option value="design">در حال طراحی</option>
                    <option value="wait_design">منتظر انتخاب طرح</option>
                    <option value="cut">فایل برش</option>
                    <option value="ready_to_build">آماده به ساخت</option>
                    <option value="wait_factory">در حال ساخت</option>
                    <option
                        value="ready"
                        style="font-weight:900;color:blue"
                    >آماده به ارسال</option>
                    <option value="ready-on">درحال ارسال</option>
                    <option value="money">منتظر تسویه مشتری</option>
                    <option value="send">ارسال شد</option>
                    <option value="cancel">کنسل کردن سفارش</option>
                </select>
                @error('data.last_status')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm font-bold text-white"
                    for="date_last_status"
                >تاریخ آخرین وضعیت:</label>
                <input
                    class="block w-full rounded-lg border-2 border-red-300 bg-red-500 p-2.5 text-center text-sm text-white outline-none focus:border-red-300 focus:ring-red-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="date_last_status"
                    data-jdp
                    type="text"
                    wire:model="data.date_last_status"
                    dir="ltr"
                >
                @error('data.date_last_status')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="relative">

                <button
                    class="top-1/2 -translate-x-1/3 transform text-white hover:text-red-300 md:absolute"
                    type="button"
                >
                    <svg
                        class="size-6"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                        />
                    </svg>

                </button>
            </div>
        </div>
    </div>
    <div class="rounded-xl bg-white p-5 dark:bg-gray-900 max-md:px-2 md:mt-3 md:shadow">
        <div class="mb-3 border-b border-gray-200 pb-3 dark:border-gray-800">
            <h3 class="text-base font-bold text-gray-700 dark:text-gray-200">جزئیات مالی سفارش</h3>
        </div>
        <div class="grid grid-cols-1 gap-3 max-md:grid-cols-2 md:grid-cols-10">
            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                    for="total_amount"
                >مبلغ کل سفارش (ریال):</label>
                <input
                    class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="total_amount"
                    type="tel"
                    onkeyup="javascript:this.value=Comma(this.value);"
                    placeholder="0"
                    wire:model="data.total_amount"
                    dir="ltr"
                >
                @error('total_amount')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                    for="deposit1"
                >جمع بیعانه (ریال) <span class="rounded-md bg-green-500 px-2 text-white">1</span></label>
                <input
                    class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="deposit1"
                    type="tel"
                    onkeyup="javascript:this.value=Comma(this.value);"
                    placeholder="0"
                    wire:model="data.deposit1"
                    dir="ltr"
                >
                @error('deposit1')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                    for="deposit2"
                >جمع بیعانه (ریال) <span class="rounded-md bg-green-500 px-2 text-white">2</span></label>
                <input
                    class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="deposit2"
                    type="tel"
                    onkeyup="javascript:this.value=Comma(this.value);"
                    placeholder="0"
                    wire:model="data.deposit2"
                    dir="ltr"
                >
                @error('deposit2')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                    for="package_type"
                >نوع پکیج همراه:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="package_type"
                    type="text"
                    wire:model="data.package_type"
                >
                @error('package_type')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                    for="total"
                >جمع بیعانه پرداخت شده (ریال):</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 disabled:bg-gray-200 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="total"
                    type="tel"
                    value="0"
                    wire:model="data.total"
                    placeholder="0"
                    dir="ltr"
                    disabled
                >
                @error('total')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                    for="remaining"
                >باقی مانده حساب (ریال):</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 disabled:bg-gray-200 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="remaining"
                    type="tel"
                    value="0"
                    wire:model="data.remaining"
                    placeholder="0"
                    dir="ltr"
                    disabled
                >
                @error('data.remaining')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                    for="remaining"
                >--- محاسبه هوشمند ---</label>
                <button
                    class="flex items-center justify-center rounded-lg bg-green-600 px-6 py-3 text-white transition-all hover:bg-green-500 disabled:bg-gray-100 disabled:bg-gray-200 disabled:text-gray-400 max-md:w-full"
                    type="button"
                    wire:loading.attr="disabled"
                    wire:click="calc"
                    wire:target="calc"
                >
                    <svg
                        class="ml-3 inline h-3 w-3 animate-spin text-green-600 dark:text-green-600"
                        role="status"
                        aria-hidden="true"
                        wire:loading=""
                        wire:target="calc"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        ></path>
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        ></path>
                    </svg>
                    <span
                        class="text-sm"
                        wire:target="calc"
                    >محاسبه هوشمند</span>
                </button>
            </div>
        </div>

    </div>
    <div class="rounded-xl bg-white p-5 dark:bg-gray-900 max-md:px-2 md:mt-3 md:shadow">
        <div class="mb-3 border-b border-gray-200 pb-3 dark:border-gray-800">
            <h3 class="text-base font-bold text-gray-700 dark:text-gray-200">آدرس سفارش دهنده</h3>
        </div>
        <div class="grid grid-cols-1 gap-3 max-md:grid-cols-2 md:grid-cols-10">
            <div class="md:col-span-4">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                    for="fullname"
                >نام و نام خانوادگی:</label>
                <div class="relative">
                    <input
                        class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="fullname"
                        type="text"
                        wire:model="data.fullname"
                    >
                    <button
                        class="absolute left-2 top-2 rounded bg-gray-200 p-1 transition-all hover:bg-gray-800 hover:text-white dark:bg-gray-900 dark:text-gray-100 dark:hover:bg-gray-800"
                        type="button"
                        @click="getSubscribeModal = true"
                    >
                        <svg
                            class="h-5 w-5"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
                            />
                        </svg>
                    </button>
                </div>
                @error('data.fullname')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                    for="phone"
                >شماره تماس اول:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-yellow-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="phone"
                    type="tel"
                    wire:model="data.phone"
                    dir="ltr"
                >
                @error('data.phone')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                    for="phone2"
                >شماره تماس دوم:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="phone2"
                    type="tel"
                    wire:model="data.phone2"
                    dir="ltr"
                >
                @error('data.phone2')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                    for="codepost"
                >کدپستی:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="codepost"
                    type="tel"
                    wire:model="data.codepost"
                    dir="ltr"
                >
                @error('data.codepost')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="max-md:col-span-2 md:col-span-10">
                <div>
                    <label
                        class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                        for="address"
                    >آدرس کامل سفارش دهنده:</label>
                    <textarea
                        class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-red-500 focus:ring-red-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-500 dark:focus:ring-red-500"
                        id="address"
                        rows="5"
                        wire:model="data.address"
                    ></textarea>
                </div>
                @error('data.address')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
        </div>
        <div x-data="{ showForm: false }">
            <div class="mt-6 flex items-center">
                <input
                    class="peer hidden"
                    id="custom"
                    type="checkbox"
                    wire:model="chResiver"
                    @click="showForm = !showForm"
                >
                <label
                    class="relative flex h-6 cursor-pointer select-none pr-8 text-sm font-bold text-gray-700 after:absolute after:right-0 after:flex after:h-6 after:w-6 after:items-center after:justify-center after:rounded after:border-2 after:border-gray-600 after:bg-white after:transition-[background-color] after:duration-300 after:ease-in after:content-[''] peer-checked:after:bg-green-300 peer-checked:after:font-bold peer-checked:after:text-green-800 peer-checked:after:transition-[background-color] peer-checked:after:duration-300 peer-checked:after:ease-in peer-checked:after:content-['x']"
                    for="custom"
                >
                    این سفارش هدیه است، وارد کردن اطلاعات شخص گیرنده
                </label>
            </div>
            <div
                class="mt-3 rounded-xl bg-red-500 p-5"
                x-show="showForm"
            >
                <div class="grid grid-cols-2 gap-3 md:grid-cols-10">
                    <div class="md:col-span-4">
                        <label
                            class="mb-2 block text-sm font-bold text-white"
                            for="r_fullname"
                        >نام و نام خانوادگی:</label>
                        <div class="relative">
                            <input
                                class="block w-full rounded-lg border-2 border-red-500 bg-red-400 p-2.5 text-sm text-white outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                                id="r_fullname"
                                type="text"
                                wire:model="data.r_fullname"
                            >
                            {{-- <button type="button" @click="getSubscribeModal = true" class=" absolute left-2 top-2 bg-gray-200 dark:bg-gray-900 dark:hover:bg-gray-800 dark:text-gray-100  hover:bg-gray-800 transition-all hover:text-white rounded p-1">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                                </svg>
                            </button> --}}
                        </div>
                        @error('data.r_fullname')
                            <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                        class="text-sm font-bold text-red-600"
                                    >{{ $message }}</span></span></div>
                        @enderror
                    </div>
                    <div class="md:col-span-2">
                        <label
                            class="mb-2 block text-sm text-white"
                            for="r_phone"
                        >شماره تماس:</label>
                        <input
                            class="block w-full rounded-lg border-2 border-red-500 bg-red-400 p-2.5 text-sm text-white outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                            id="r_phone"
                            type="tel"
                            wire:model="data.r_phone"
                            dir="ltr"
                        >
                        @error('data.r_phone')
                            <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                        class="text-sm font-bold text-red-600"
                                    >{{ $message }}</span></span></div>
                        @enderror
                    </div>
                    <div class="md:col-span-2">
                        <label
                            class="mb-2 block text-sm text-white"
                            for="r_codepost"
                        >کدپستی:</label>
                        <input
                            class="block w-full rounded-lg border-2 border-red-500 bg-red-400 p-2.5 text-sm text-white outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                            id="r_codepost"
                            type="tel"
                            wire:model="data.r_codepost"
                            dir="ltr"
                        >
                        @error('data.r_codepost')
                            <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                        class="text-sm font-bold text-red-600"
                                    >{{ $message }}</span></span></div>
                        @enderror
                    </div>
                    <div class="max-md:col-span-2 md:col-span-10">
                        <div>
                            <label
                                class="mb-2 block text-sm text-white"
                                for="r_address"
                            >آدرس کامل سفارش دهنده:</label>
                            <textarea
                                class="block w-full rounded-lg border-2 border-red-500 bg-red-400 p-2.5 text-sm text-gray-900 focus:border-red-500 focus:ring-red-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-500 dark:focus:ring-red-500"
                                id="r_address"
                                rows="5"
                                wire:model="data.r_address"
                            ></textarea>
                        </div>
                        @error('data.r_address')
                            <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                        class="text-sm font-bold text-red-600"
                                    >{{ $message }}</span></span></div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="rounded-xl bg-white p-5 dark:bg-gray-900 max-md:px-2 md:mt-3 md:shadow">
        <div class="mb-3 border-b border-gray-200 pb-3 dark:border-gray-800">
            <h3 class="text-base font-bold text-gray-700 dark:text-gray-200">فیش و مستندات</h3>
        </div>
        <div class="mt-6 grid grid-cols-1 gap-3 md:grid-cols-2">
            <div class="relative border-2 border-gray-100 p-3 dark:border-gray-800">
                <span class="absolute -top-3 rounded-lg bg-gray-800 px-6 py-1 text-sm text-white">فیش های واریزی</span>
                <div class="mt-4 grid grid-cols-1 gap-3 md:grid-cols-2">

                    @include('components.order.image1')
                    @include('components.order.image2')
                </div>
            </div>
            <div class="relative border-2 border-gray-100 p-3 dark:border-gray-800">
                <span class="absolute -top-3 rounded-lg bg-gray-800 px-6 py-1 text-sm text-white">تصاویر کار</span>
                <div class="mt-4 grid grid-cols-1 gap-3 md:grid-cols-2">

                    @include('components.order.image3')
                    @include('components.order.image4')
                </div>
            </div>
        </div>
        <div class="mt-6 grid grid-cols-1 gap-3">
            <div class="relative border-2 border-gray-100 p-3 dark:border-gray-800">
                <span class="absolute -top-3 rounded-lg bg-gray-800 px-6 py-1 text-sm text-white">تصاویر کار</span>
                <div class="mt-4 grid grid-cols-1 gap-3 md:grid-cols-3">

                    @include('components.order.image5')
                    @include('components.order.image6')
                    @include('components.order.image7')

                </div>
            </div>
        </div>
    </div>
    <div
        class="z-50 z-50 rounded-xl bg-white p-3 dark:bg-gray-900 max-md:fixed max-md:bottom-0 max-md:left-0 max-md:w-full max-md:p-3 max-md:px-2 max-md:pb-5 md:mt-3 md:p-5 md:shadow">
        <div class="flex flex-row-reverse items-center">
            <button
                class="flex items-center justify-center rounded-lg bg-green-600 px-6 py-3 text-white transition-all hover:bg-green-500 disabled:bg-gray-200 disabled:text-gray-400 max-md:w-full"
                type="submit"
                wire:target="store"
            >
                <svg
                    class="ml-3 inline h-6 w-6 animate-spin text-green-600 dark:text-green-600"
                    role="status"
                    aria-hidden="true"
                    wire:loading
                    wire:target="store"
                    viewBox="0 0 100 101"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="#E5E7EB"
                    />
                    <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentColor"
                    />
                </svg>
                <span>ثبت نهایی فرم</span>
            </button>
        </div>
    </div>
    <script>
        function deferredImageHandler() {
            return {
                selectedFile: null,
                isUploading: false,
                previewUrl: null,

                handleFileChange(event, imgRef) {
                    const file = event.target.files[0];
                    if (file) {
                        this.selectedFile = file;
                        this.previewUrl = URL.createObjectURL(file);
                        imgRef.src = this.previewUrl;
                    }
                },

                async uploadFile() {
                    if (!this.selectedFile) return;

                    this.isUploading = true;

                    try {
                        // ارسال فایل به Livewire component
                        await @this.upload('data.image1', this.selectedFile, (uploadedFilename) => {
                            // موفقیت آپلود
                            this.selectedFile = null;
                            this.isUploading = false;

                            // نمایش پیام موفقیت
                            window.dispatchEvent(new CustomEvent('show-alert', {
                                detail: {
                                    type: 'success',
                                    message: 'تصویر با موفقیت آپلود شد'
                                }
                            }));
                        }, (error) => {
                            // خطا در آپلود
                            this.isUploading = false;
                            console.error('Upload error:', error);

                            // نمایش پیام خطا
                            window.dispatchEvent(new CustomEvent('show-alert', {
                                detail: {
                                    type: 'error',
                                    message: 'خطا در آپلود تصویر'
                                }
                            }));
                        });
                    } catch (error) {
                        this.isUploading = false;
                        console.error('Upload error:', error);
                    }
                },

                showModal(imgRef) {
                    // نمایش مودال تصویر
                    if (imgRef.src && imgRef.src !== '/assets/images/document.png') {
                        // کد نمایش مودال
                    }
                },

                deleteImage(imgRef) {
                    imgRef.src = '/assets/images/document.png';
                    this.selectedFile = null;
                    this.previewUrl = null;
                    if (this.$refs.fileInput) {
                        this.$refs.fileInput.value = null;
                    }
                }
            };
        }
    </script>

</form>
