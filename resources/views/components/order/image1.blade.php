<div
    class="relative flex flex-col items-center justify-center border-2 border-dashed border-red-500 p-5"
    x-data="deferredImageHandler()"
>

    <div wire:loading>
        @include('layouts.tools.loading-dot')
    </div>

    <button
        class="block"
        type="button"
    >
        @if (is_object($data['image1']))
            <img
                class="mx-auto h-32 w-32"
                src="{{ $data['image1']->temporaryUrl() }}"
                x-ref="img1"
                @click="setImage('{{ $data['image1']->temporaryUrl() }}'), lock = true"
            />
        @elseif($data['image1'] != null)
            <img
                class="mx-auto h-32 w-32"
                src="{{ $data['image1'] ? $image_server_url_1 . ltrim($data['image1'], '/\\') : '/assets/images/document.png' }}"
                x-ref="img1"
                @click="setImage($event.target.src), lock = true"
                onerror="
                this.onerror=null;
                this.src='{{ $data['image1'] ? $image_server_url_2 . ltrim($data['image1'], '/\\') : '/assets/images/document.png' }}';
                this.onerror=function(){this.src='{{ $data['image1'] ? $image_server_url_3 . ltrim($data['image1'], '/\\') : '/assets/images/document.png' }}';};
            "
            />
        @else
            <img
                class="mx-auto h-32 w-32"
                src="/assets/images/document.png"
                x-ref="img1"
                @click="setImage('/assets/images/document.png'), lock = true"
            />
        @endif
    </button>
    <div class="flex items-center gap-2 pt-3">

        <label
            class="flex cursor-pointer items-center rounded-md bg-white p-2 px-6 text-center shadow transition-all hover:bg-gray-200 dark:bg-gray-800 hover:dark:bg-gray-700"
            for="image1"
        >
            <input
                class="hidden"
                id="image1"
                type="file"
                accept="image/*"
                @change="handleFileChange($event, $refs.img1)"
                x-ref="fileInput"
            >
            <span class="text-sm text-gray-700 dark:text-gray-200">بیعانه 1</span>
        </label>
        <button
            class="flex cursor-pointer items-center rounded-md bg-green-600 p-2 px-4 text-center shadow transition-all hover:bg-green-500 disabled:bg-gray-400"
            type="button"
            @click="uploadFile()"
            x-show="selectedFile && !isUploading"
            :disabled="isUploading"
        >
            <span class="text-sm text-white">آپلود</span>
        </button>
        <div
            class="flex items-center rounded-md bg-blue-600 p-2 px-4 text-center shadow"
            x-show="isUploading"
        >
            <svg
                class="mr-2 h-4 w-4 animate-spin text-white"
                fill="none"
                viewBox="0 0 24 24"
            >
                <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                ></circle>
                <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
            </svg>
            <span class="text-sm text-white">در حال آپلود...</span>
        </div>
        <button
            class="flex cursor-pointer items-center rounded-md bg-white p-1.5 text-center shadow transition-all hover:bg-gray-200 dark:bg-gray-800 hover:dark:bg-gray-700"
            type="button"
            @click="showScreenShotModal = true, screenShotItemNumber = 1 "
        >
            <svg
                class="size-5"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M6.827 6.175A2.31 2.31 0 0 1 5.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 0 0-1.134-.175 2.31 2.31 0 0 1-1.64-1.055l-.822-1.316a2.192 2.192 0 0 0-1.736-1.039 48.774 48.774 0 0 0-5.232 0 2.192 2.192 0 0 0-1.736 1.039l-.821 1.316Z"
                />
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M16.5 12.75a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM18.75 10.5h.008v.008h-.008V10.5Z"
                />
            </svg>

        </button>
        <button
            class="flex items-center rounded-md bg-red-600 p-2 px-2 text-center shadow transition-all hover:bg-red-500"
            type="button"
            wire:click="remove('image1')"
        >
            <svg
                class="h-4 w-4 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                />
            </svg>
        </button>
    </div>
    @error('data.image1')
        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                    class="text-sm font-bold text-red-600">{{ $message }}</span></span></div>
    @enderror
</div>
